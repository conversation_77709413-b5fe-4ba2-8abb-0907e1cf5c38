<?php
/**
 * 演示数据生成脚本
 * 用于创建一些示例字体数据进行测试
 */

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/includes/functions.php';

// 检查是否已有数据
$existingFonts = $fontManager->getFonts('', '', 1, 1);
if ($existingFonts['total'] > 0) {
    echo "数据库中已有字体数据，跳过演示数据生成。\n";
    exit;
}

// 演示字体数据
$demoFonts = [
    [
        'name' => 'Helvetica Neue',
        'filename' => 'helvetica-neue.ttf',
        'filepath' => FONTS_DIR . 'helvetica-neue.ttf',
        'family' => 'Helvetica Neue',
        'style' => 'Regular',
        'weight' => '400',
        'category' => 'sans-serif',
        'file_size' => 245760,
        'description' => '经典的无衬线字体，广泛用于现代设计',
        'tags' => '现代, 简洁, 经典'
    ],
    [
        'name' => 'Times New Roman',
        'filename' => 'times-new-roman.ttf',
        'filepath' => FONTS_DIR . 'times-new-roman.ttf',
        'family' => 'Times New Roman',
        'style' => 'Regular',
        'weight' => '400',
        'category' => 'serif',
        'file_size' => 189440,
        'description' => '传统的衬线字体，适合正文阅读',
        'tags' => '传统, 正文, 阅读'
    ],
    [
        'name' => 'Brush Script MT',
        'filename' => 'brush-script-mt.ttf',
        'filepath' => FONTS_DIR . 'brush-script-mt.ttf',
        'family' => 'Brush Script MT',
        'style' => 'Regular',
        'weight' => '400',
        'category' => 'handwriting',
        'file_size' => 156672,
        'description' => '手写风格的字体，适合装饰性文字',
        'tags' => '手写, 装饰, 艺术'
    ],
    [
        'name' => 'Courier New',
        'filename' => 'courier-new.ttf',
        'filepath' => FONTS_DIR . 'courier-new.ttf',
        'family' => 'Courier New',
        'style' => 'Regular',
        'weight' => '400',
        'category' => 'monospace',
        'file_size' => 234567,
        'description' => '等宽字体，适合代码和技术文档',
        'tags' => '等宽, 代码, 技术'
    ],
    [
        'name' => 'Impact',
        'filename' => 'impact.ttf',
        'filepath' => FONTS_DIR . 'impact.ttf',
        'family' => 'Impact',
        'style' => 'Regular',
        'weight' => '900',
        'category' => 'display',
        'file_size' => 178234,
        'description' => '粗体装饰字体，适合标题和强调',
        'tags' => '粗体, 标题, 强调'
    ],
    [
        'name' => 'Arial Black',
        'filename' => 'arial-black.ttf',
        'filepath' => FONTS_DIR . 'arial-black.ttf',
        'family' => 'Arial',
        'style' => 'Black',
        'weight' => '900',
        'category' => 'sans-serif',
        'file_size' => 198765,
        'description' => 'Arial字体的粗体版本',
        'tags' => '无衬线, 粗体, 现代'
    ],
    [
        'name' => 'Georgia',
        'filename' => 'georgia.ttf',
        'filepath' => FONTS_DIR . 'georgia.ttf',
        'family' => 'Georgia',
        'style' => 'Regular',
        'weight' => '400',
        'category' => 'serif',
        'file_size' => 167890,
        'description' => '为屏幕阅读优化的衬线字体',
        'tags' => '衬线, 屏幕, 优化'
    ],
    [
        'name' => 'Verdana',
        'filename' => 'verdana.ttf',
        'filepath' => FONTS_DIR . 'verdana.ttf',
        'family' => 'Verdana',
        'style' => 'Regular',
        'weight' => '400',
        'category' => 'sans-serif',
        'file_size' => 201234,
        'description' => '为小尺寸显示设计的无衬线字体',
        'tags' => '无衬线, 小尺寸, 清晰'
    ]
];

echo "开始生成演示数据...\n";

$successCount = 0;
$errorCount = 0;

foreach ($demoFonts as $fontData) {
    try {
        // 创建一个空的字体文件（仅用于演示）
        if (!file_exists($fontData['filepath'])) {
            file_put_contents($fontData['filepath'], '');
        }
        
        $fontId = $fontManager->addFont($fontData);
        
        if ($fontId) {
            echo "✓ 添加字体: {$fontData['name']}\n";
            $successCount++;
        } else {
            echo "✗ 添加字体失败: {$fontData['name']}\n";
            $errorCount++;
        }
    } catch (Exception $e) {
        echo "✗ 添加字体出错: {$fontData['name']} - {$e->getMessage()}\n";
        $errorCount++;
    }
}

echo "\n演示数据生成完成！\n";
echo "成功: {$successCount} 个字体\n";
echo "失败: {$errorCount} 个字体\n";

if ($successCount > 0) {
    echo "\n现在可以访问网站查看演示数据。\n";
    echo "注意：这些是演示数据，字体文件为空文件，仅用于界面测试。\n";
}
?>
