/* Compare Page Styles */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e5e5e5;
}

.page-header h1 {
    margin: 0;
    font-size: 28px;
    font-weight: 700;
}

.compare-controls {
    display: flex;
    align-items: center;
    gap: 20px;
}

.empty-state {
    text-align: center;
    padding: 80px 20px;
    color: #666;
}

.empty-icon {
    font-size: 64px;
    margin-bottom: 20px;
}

.empty-state h2 {
    margin-bottom: 10px;
    color: #333;
}

.empty-state p {
    margin-bottom: 30px;
    font-size: 16px;
}

.compare-controls-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.layout-control {
    display: flex;
    align-items: center;
    gap: 10px;
}

.layout-btn {
    padding: 8px 16px;
    border: 1px solid #ddd;
    background: #fff;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s;
}

.layout-btn.active {
    background: #000;
    color: #fff;
    border-color: #000;
}

/* Compare Grid */
.compare-grid {
    display: grid;
    gap: 20px;
}

.compare-grid.vertical {
    grid-template-columns: 1fr;
}

.compare-grid.horizontal {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.compare-item {
    background: #fff;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.2s;
}

.compare-item:hover {
    border-color: #000;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.compare-preview {
    padding: 40px 20px;
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: #fafafa;
    border-bottom: 1px solid #e5e5e5;
    line-height: 1.2;
    word-break: break-word;
}

.compare-info {
    padding: 20px;
}

.compare-info .font-name {
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 18px;
}

.compare-info .font-meta {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
}

.compare-actions {
    display: flex;
    gap: 10px;
}

.remove-btn {
    background: #dc3545;
    color: #fff;
    border-color: #dc3545;
}

.remove-btn:hover {
    background: #c82333;
    border-color: #bd2130;
}

/* Vertical Layout Specific */
.compare-grid.vertical .compare-item {
    display: flex;
    align-items: stretch;
}

.compare-grid.vertical .compare-preview {
    flex: 1;
    min-height: 80px;
    padding: 20px;
    border-bottom: none;
    border-right: 1px solid #e5e5e5;
}

.compare-grid.vertical .compare-info {
    width: 300px;
    flex-shrink: 0;
}

/* Responsive */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .compare-controls {
        justify-content: space-between;
    }
    
    .compare-controls-section {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .compare-grid.horizontal {
        grid-template-columns: 1fr;
    }
    
    .compare-grid.vertical .compare-item {
        flex-direction: column;
    }
    
    .compare-grid.vertical .compare-preview {
        border-right: none;
        border-bottom: 1px solid #e5e5e5;
    }
    
    .compare-grid.vertical .compare-info {
        width: auto;
    }
    
    .compare-actions {
        flex-wrap: wrap;
    }
}
