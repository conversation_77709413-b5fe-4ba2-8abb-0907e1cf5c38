<?php
require_once __DIR__ . '/../config/config.php';

class FontParser {
    private $fontFile;
    private $fontData;
    
    public function __construct($fontFile) {
        $this->fontFile = $fontFile;
        $this->fontData = [];
    }
    
    public function parse() {
        if (!file_exists($this->fontFile)) {
            throw new Exception('字体文件不存在');
        }
        
        $extension = strtolower(pathinfo($this->fontFile, PATHINFO_EXTENSION));
        
        switch ($extension) {
            case 'ttf':
            case 'otf':
                return $this->parseTrueTypeFont();
            case 'woff':
            case 'woff2':
                return $this->parseWebFont();
            default:
                throw new Exception('不支持的字体格式');
        }
    }
    
    private function parseTrueTypeFont() {
        $handle = fopen($this->fontFile, 'rb');
        if (!$handle) {
            throw new Exception('无法读取字体文件');
        }
        
        try {
            // 读取字体头部信息
            $header = fread($handle, 12);
            if (strlen($header) < 12) {
                throw new Exception('字体文件格式错误');
            }
            
            // 解析表目录
            $tables = $this->parseTableDirectory($handle);
            
            // 解析name表获取字体信息
            $nameTable = $this->parseNameTable($handle, $tables);
            
            // 解析OS/2表获取字体权重等信息
            $os2Table = $this->parseOS2Table($handle, $tables);
            
            fclose($handle);
            
            return [
                'name' => $nameTable['fontName'] ?? basename($this->fontFile, '.' . pathinfo($this->fontFile, PATHINFO_EXTENSION)),
                'family' => $nameTable['fontFamily'] ?? '',
                'style' => $nameTable['fontStyle'] ?? 'Regular',
                'weight' => $os2Table['weight'] ?? '400',
                'category' => $this->detectCategory($nameTable),
                'file_size' => filesize($this->fontFile)
            ];
            
        } catch (Exception $e) {
            fclose($handle);
            // 如果解析失败，返回基本信息
            return $this->getFallbackInfo();
        }
    }
    
    private function parseWebFont() {
        // 对于Web字体，我们主要从文件名推断信息
        return $this->getFallbackInfo();
    }
    
    private function parseTableDirectory($handle) {
        fseek($handle, 4); // 跳过版本号
        $numTables = $this->readUInt16($handle);
        fseek($handle, 12); // 跳到表目录
        
        $tables = [];
        for ($i = 0; $i < $numTables; $i++) {
            $tag = fread($handle, 4);
            $checksum = $this->readUInt32($handle);
            $offset = $this->readUInt32($handle);
            $length = $this->readUInt32($handle);
            
            $tables[$tag] = [
                'offset' => $offset,
                'length' => $length
            ];
        }
        
        return $tables;
    }
    
    private function parseNameTable($handle, $tables) {
        if (!isset($tables['name'])) {
            return [];
        }
        
        fseek($handle, $tables['name']['offset']);
        
        $format = $this->readUInt16($handle);
        $count = $this->readUInt16($handle);
        $stringOffset = $this->readUInt16($handle);
        
        $names = [];
        
        for ($i = 0; $i < $count; $i++) {
            $platformID = $this->readUInt16($handle);
            $encodingID = $this->readUInt16($handle);
            $languageID = $this->readUInt16($handle);
            $nameID = $this->readUInt16($handle);
            $length = $this->readUInt16($handle);
            $offset = $this->readUInt16($handle);
            
            // 我们主要关心英文名称 (platformID = 3, languageID = 1033)
            if ($platformID == 3 && $languageID == 1033) {
                $currentPos = ftell($handle);
                fseek($handle, $tables['name']['offset'] + $stringOffset + $offset);
                $nameString = fread($handle, $length);
                
                // Unicode字符串，需要转换
                $nameString = mb_convert_encoding($nameString, 'UTF-8', 'UTF-16BE');
                
                switch ($nameID) {
                    case 1: // Font Family
                        $names['fontFamily'] = $nameString;
                        break;
                    case 2: // Font Style
                        $names['fontStyle'] = $nameString;
                        break;
                    case 4: // Full Font Name
                        $names['fontName'] = $nameString;
                        break;
                }
                
                fseek($handle, $currentPos);
            }
        }
        
        return $names;
    }
    
    private function parseOS2Table($handle, $tables) {
        if (!isset($tables['OS/2'])) {
            return [];
        }
        
        fseek($handle, $tables['OS/2']['offset']);
        
        $version = $this->readUInt16($handle);
        fseek($handle, $tables['OS/2']['offset'] + 4); // 跳到usWeightClass
        $weightClass = $this->readUInt16($handle);
        
        return [
            'weight' => (string)$weightClass
        ];
    }
    
    private function readUInt16($handle) {
        $data = fread($handle, 2);
        return unpack('n', $data)[1];
    }
    
    private function readUInt32($handle) {
        $data = fread($handle, 4);
        return unpack('N', $data)[1];
    }
    
    private function detectCategory($nameInfo) {
        $name = strtolower(($nameInfo['fontFamily'] ?? '') . ' ' . ($nameInfo['fontName'] ?? ''));
        
        // 基于字体名称的简单分类
        if (preg_match('/serif/i', $name) && !preg_match('/sans/i', $name)) {
            return 'serif';
        }
        
        if (preg_match('/sans|gothic/i', $name)) {
            return 'sans-serif';
        }
        
        if (preg_match('/script|hand|brush|callig/i', $name)) {
            return 'handwriting';
        }
        
        if (preg_match('/mono|code|console/i', $name)) {
            return 'monospace';
        }
        
        if (preg_match('/display|decorat|fancy/i', $name)) {
            return 'display';
        }
        
        return 'other';
    }
    
    private function getFallbackInfo() {
        $filename = basename($this->fontFile);
        $nameWithoutExt = pathinfo($filename, PATHINFO_FILENAME);
        
        return [
            'name' => $nameWithoutExt,
            'family' => $nameWithoutExt,
            'style' => 'Regular',
            'weight' => '400',
            'category' => $this->detectCategory(['fontName' => $nameWithoutExt]),
            'file_size' => filesize($this->fontFile)
        ];
    }
}
