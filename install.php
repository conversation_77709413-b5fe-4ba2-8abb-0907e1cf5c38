<?php
/**
 * XFont 安装脚本
 * 检查系统要求并初始化应用
 */

// 检查PHP版本
if (version_compare(PHP_VERSION, '7.4.0', '<')) {
    die('错误: 需要PHP 7.4或更高版本，当前版本: ' . PHP_VERSION);
}

// 检查必需的扩展
$requiredExtensions = ['pdo', 'pdo_sqlite', 'json', 'mbstring'];
$missingExtensions = [];

foreach ($requiredExtensions as $ext) {
    if (!extension_loaded($ext)) {
        $missingExtensions[] = $ext;
    }
}

if (!empty($missingExtensions)) {
    die('错误: 缺少必需的PHP扩展: ' . implode(', ', $missingExtensions));
}

// 检查目录权限
$directories = [
    'uploads' => 'uploads/',
    'assets/fonts' => 'assets/fonts/',
    'database' => 'database/',
    'logs' => 'logs/'
];

$permissionErrors = [];

foreach ($directories as $name => $path) {
    if (!is_dir($path)) {
        if (!mkdir($path, 0755, true)) {
            $permissionErrors[] = "无法创建目录: {$path}";
        }
    } elseif (!is_writable($path)) {
        $permissionErrors[] = "目录不可写: {$path}";
    }
}

if (!empty($permissionErrors)) {
    die('错误: 目录权限问题:\n' . implode('\n', $permissionErrors));
}

// 初始化应用
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/includes/functions.php';

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XFont 安装</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            line-height: 1.6;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .logo {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 10px;
        }
        .check-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
        }
        .check-item.success {
            background: #d4edda;
            color: #155724;
        }
        .check-item.error {
            background: #f8d7da;
            color: #721c24;
        }
        .check-icon {
            margin-right: 10px;
            font-weight: bold;
        }
        .install-btn {
            background: #000;
            color: #fff;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 20px;
        }
        .install-btn:hover {
            background: #333;
        }
        .install-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .section h3 {
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">XFont</div>
        <p>个人字体管理系统安装向导</p>
    </div>

    <div class="section">
        <h3>系统检查</h3>

        <div class="check-item success">
            <span class="check-icon">✓</span>
            PHP版本: <?php echo PHP_VERSION; ?> (要求 >= 7.4)
        </div>

        <?php foreach ($requiredExtensions as $ext): ?>
        <div class="check-item success">
            <span class="check-icon">✓</span>
            PHP扩展: <?php echo $ext; ?>
        </div>
        <?php endforeach; ?>

        <?php foreach ($directories as $name => $path): ?>
        <div class="check-item success">
            <span class="check-icon">✓</span>
            目录权限: <?php echo $path; ?>
        </div>
        <?php endforeach; ?>
    </div>

    <div class="section">
        <h3>数据库初始化</h3>
        <?php
        try {
            // 测试数据库连接
            $db = new Database();
            echo '<div class="check-item success">';
            echo '<span class="check-icon">✓</span>';
            echo 'SQLite数据库已成功初始化';
            echo '</div>';

            // 检查是否有数据
            $fontCount = $fontManager->getFonts('', '', 1, 1)['total'];
            if ($fontCount > 0) {
                echo '<div class="check-item success">';
                echo '<span class="check-icon">✓</span>';
                echo "数据库中已有 {$fontCount} 个字体";
                echo '</div>';
            } else {
                echo '<div class="check-item" style="background: #fff3cd; color: #856404;">';
                echo '<span class="check-icon">!</span>';
                echo '数据库为空，可以开始使用或生成演示数据';
                echo '</div>';
            }

            $dbReady = true;
        } catch (Exception $e) {
            echo '<div class="check-item error">';
            echo '<span class="check-icon">✗</span>';
            echo '数据库初始化失败: ' . $e->getMessage();
            echo '</div>';
            $dbReady = false;
        }
        ?>
    </div>

    <div class="section">
        <h3>安装完成</h3>
        <?php if ($dbReady): ?>
        <p>✅ 系统检查通过，XFont已准备就绪！</p>

        <div style="margin: 20px 0;">
            <a href="index.php" class="install-btn">进入主页</a>
            <a href="demo-data.php" class="install-btn" style="background: #6c757d; margin-left: 10px;">生成演示数据</a>
        </div>

        <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
            <h4>下一步:</h4>
            <ul>
                <li>访问主页开始使用XFont</li>
                <li>点击"上传字体"按钮添加你的字体文件</li>
                <li>或者生成演示数据来体验功能</li>
                <li>删除此安装文件以提高安全性</li>
            </ul>
        </div>

        <?php else: ?>
        <p>❌ 安装过程中遇到问题，请检查上述错误信息。</p>
        <button class="install-btn" disabled>安装未完成</button>
        <?php endif; ?>
    </div>

    <div class="section">
        <h3>技术信息</h3>
        <ul>
            <li><strong>PHP版本:</strong> <?php echo PHP_VERSION; ?></li>
            <li><strong>Web服务器:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? '未知'; ?></li>
            <li><strong>文档根目录:</strong> <?php echo $_SERVER['DOCUMENT_ROOT'] ?? '未知'; ?></li>
            <li><strong>当前目录:</strong> <?php echo __DIR__; ?></li>
            <li><strong>URL重写:</strong> <?php echo (isset($_SERVER['HTTP_MOD_REWRITE']) || function_exists('apache_get_modules') && in_array('mod_rewrite', apache_get_modules())) ? '支持' : '需要配置'; ?></li>
        </ul>
    </div>
</body>
</html>
