<?php
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/includes/functions.php';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - 个人字体管理</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔤</text></svg>">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <a href="index.php" class="logo"><?php echo APP_NAME; ?></a>

            <div class="header-controls">
                <div class="search-box">
                    <input type="text" id="searchInput" class="search-input" placeholder="搜索字体...">
                    <button id="searchBtn" class="search-btn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="11" cy="11" r="8"></circle>
                            <path d="m21 21-4.35-4.35"></path>
                        </svg>
                    </button>
                </div>

                <input type="text" id="previewTextInput" class="preview-text-input"
                       placeholder="自定义预览文字..." value="<?php echo htmlspecialchars($_SESSION['preview_text']); ?>">

                <div class="nav-links">
                    <a href="favorites.php" class="nav-link">收藏</a>
                    <a href="compare.php" class="nav-link">对比</a>
                </div>

                <button id="uploadBtn" class="upload-btn">上传字体</button>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav">
        <div class="nav-container">
            <ul class="nav-tabs">
                <!-- 分类标签将通过JavaScript动态加载 -->
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main">
        <!-- View Controls -->
        <div class="view-controls">
            <div class="view-toggle">
                <button class="view-btn active" data-view="grid">网格视图</button>
                <button class="view-btn" data-view="list">列表视图</button>
            </div>

            <div class="font-size-control">
                <label for="fontSizeSlider">字体大小:</label>
                <input type="range" id="fontSizeSlider" class="font-size-slider"
                       min="12" max="72" value="24" step="2">
                <span id="fontSizeValue">24px</span>
            </div>
        </div>

        <!-- Font Grid -->
        <div id="fontGrid" class="font-grid">
            <!-- 字体卡片将通过JavaScript动态加载 -->
        </div>

        <!-- Pagination -->
        <div id="pagination" class="pagination">
            <!-- 分页将通过JavaScript动态加载 -->
        </div>
    </main>

    <!-- Upload Modal -->
    <div id="uploadModal" class="modal upload-modal">
        <div class="modal-content">
            <h2>上传字体文件</h2>
            <p>支持格式: TTF, OTF, WOFF, WOFF2</p>

            <div id="uploadArea" class="upload-area">
                <div>
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                        <polyline points="14,2 14,8 20,8"></polyline>
                        <line x1="16" y1="13" x2="8" y2="13"></line>
                        <line x1="16" y1="17" x2="8" y2="17"></line>
                        <polyline points="10,9 9,9 8,9"></polyline>
                    </svg>
                    <h3>拖拽文件到此处或点击选择</h3>
                    <p>可以同时上传多个字体文件</p>
                </div>
            </div>

            <input type="file" id="fileInput" class="file-input" multiple accept=".ttf,.otf,.woff,.woff2">

            <div id="uploadProgress" class="upload-progress" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 0%"></div>
                </div>
                <p>正在上传...</p>
            </div>
        </div>
    </div>

    <!-- Font Detail Modal -->
    <div id="fontDetailModal" class="modal font-detail-modal">
        <div class="modal-content">
            <!-- 字体详情内容将通过JavaScript动态加载 -->
        </div>
    </div>

    <!-- Comparison Modal -->
    <div id="comparisonModal" class="modal comparison-modal">
        <div class="modal-content">
            <!-- 字体对比内容将通过JavaScript动态加载 -->
        </div>
    </div>

    <!-- Favorites Modal -->
    <div id="favoritesModal" class="modal favorites-modal">
        <div class="modal-content">
            <!-- 收藏夹内容将通过JavaScript动态加载 -->
        </div>
    </div>

    <script src="assets/js/app.js"></script>
</body>
</html>
