# XFont - 个人字体管理网站

一个现代化的个人字体管理系统，使用PHP和SQLite构建，具有简洁的黑白设计风格。

## 功能特性

### 核心功能
- **字体上传**: 支持TTF、OTF、WOFF、WOFF2格式，可批量上传
- **智能识别**: 自动解析字体信息（名称、族、样式、字重、分类）
- **字体浏览**: 瀑布流和列表两种视图模式
- **分类管理**: 自动分类（衬线、无衬线、手写、毛笔等）
- **搜索功能**: 支持按名称、族、标签搜索
- **字体预览**: 自定义预览文字，实时调整字体大小

### 用户功能
- **收藏管理**: 收藏喜欢的字体
- **字体对比**: 最多同时对比10个字体
- **字体下载**: 一键下载字体文件
- **详情编辑**: 编辑字体名称、描述、标签等信息

### 技术特性
- **响应式设计**: 适配桌面和移动设备
- **现代UI**: 简约黑白风格，参考Awwwards设计
- **高性能**: SQLite数据库，快速字体预览
- **易部署**: 纯PHP实现，无需复杂环境

## 系统要求

- PHP 7.4 或更高版本
- SQLite 3 支持
- Apache/Nginx Web服务器
- 支持.htaccess（Apache）或相应的URL重写配置

## 安装部署

### 1. 下载代码
```bash
git clone <repository-url> xfont
cd xfont
```

### 2. 配置Web服务器

#### Apache配置
确保启用了mod_rewrite模块，项目根目录已包含.htaccess文件。

#### Nginx配置
在server块中添加以下配置：
```nginx
location / {
    try_files $uri $uri/ /index.php?$query_string;
}

location ~ ^/api/(.*)$ {
    try_files $uri /api/$1;
}

location ~ ^/font/([0-9]+)/?$ {
    try_files $uri /font-detail.php?id=$1;
}

location ~ ^/(favorites|compare)/?$ {
    try_files $uri /$1.php;
}

location ~* \.(ttf|otf|woff|woff2)$ {
    add_header Access-Control-Allow-Origin "*";
    expires 1M;
}
```

### 3. 设置权限
```bash
chmod 755 uploads/
chmod 755 assets/fonts/
chmod 755 database/
chmod 755 logs/
```

### 4. 访问网站
在浏览器中访问你的域名，系统会自动创建数据库和必要的目录。

## 使用说明

### 上传字体
1. 点击页面右上角的"上传字体"按钮
2. 拖拽字体文件到上传区域或点击选择文件
3. 支持同时上传多个字体文件
4. 系统会自动解析字体信息并分类

### 浏览字体
1. 主页显示所有字体，按分类标签页组织
2. 可以切换网格视图和列表视图
3. 调整字体大小滑块来改变预览大小
4. 修改预览文字输入框来自定义预览内容

### 搜索字体
1. 使用页面顶部的搜索框
2. 支持按字体名称、族、标签搜索
3. 搜索结果实时显示

### 管理字体
1. 点击字体卡片进入详情页
2. 在详情页可以编辑字体信息
3. 支持修改名称、分类、描述、标签
4. 可以删除不需要的字体

### 收藏和对比
1. 在字体卡片上点击"收藏"按钮添加到收藏夹
2. 点击"对比"按钮添加到对比列表
3. 通过顶部导航访问收藏夹和对比页面
4. 对比页面支持垂直和水平两种布局

## 目录结构

```
xfont/
├── api/                    # API接口
│   ├── upload.php         # 字体上传
│   ├── fonts.php          # 字体管理
│   ├── favorites.php      # 收藏管理
│   └── compare.php        # 对比管理
├── assets/                # 静态资源
│   ├── css/              # 样式文件
│   ├── js/               # JavaScript文件
│   └── fonts/            # 上传的字体文件
├── config/               # 配置文件
│   ├── config.php        # 全局配置
│   └── database.php      # 数据库类
├── includes/             # 包含文件
│   ├── functions.php     # 字体管理类
│   └── font-parser.php   # 字体解析类
├── database/             # 数据库文件
├── uploads/              # 临时上传目录
├── logs/                 # 日志文件
├── index.php             # 主页
├── font-detail.php       # 字体详情页
├── favorites.php         # 收藏页面
├── compare.php           # 对比页面
├── .htaccess            # Apache重写规则
└── README.md            # 说明文档
```

## 配置说明

主要配置项在 `config/config.php` 文件中：

- `MAX_FILE_SIZE`: 最大文件上传大小（默认50MB）
- `FONTS_PER_PAGE`: 每页显示字体数量（默认24个）
- `ALLOWED_EXTENSIONS`: 允许的字体文件格式
- `FONT_CATEGORIES`: 字体分类定义

## 故障排除

### 上传失败
1. 检查uploads和assets/fonts目录权限
2. 确认PHP上传限制设置
3. 检查磁盘空间

### 字体预览不显示
1. 确认字体文件路径正确
2. 检查Web服务器MIME类型配置
3. 确认字体文件没有损坏

### 数据库错误
1. 检查database目录权限
2. 确认SQLite扩展已启用
3. 查看logs目录下的错误日志

## 技术栈

- **后端**: PHP 8+
- **数据库**: SQLite 3
- **前端**: HTML5, CSS3, JavaScript (ES6+)
- **字体解析**: 自定义PHP字体解析器
- **设计风格**: 现代简约黑白风格

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 更新日志

### v1.0.0
- 初始版本发布
- 基础字体管理功能
- 上传、浏览、搜索、收藏、对比功能
- 响应式设计
- 字体信息自动识别
