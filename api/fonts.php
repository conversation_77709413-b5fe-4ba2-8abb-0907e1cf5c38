<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/functions.php';

header('Content-Type: application/json; charset=utf-8');

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

switch ($method) {
    case 'GET':
        handleGetRequest($action);
        break;
    case 'POST':
        handlePostRequest($action);
        break;
    case 'PUT':
        handlePutRequest($action);
        break;
    case 'DELETE':
        handleDeleteRequest($action);
        break;
    default:
        errorResponse('不支持的请求方法', 405);
}

function handleGetRequest($action) {
    global $fontManager;

    switch ($action) {
        case 'list':
            $category = $_GET['category'] ?? '';
            $search = $_GET['search'] ?? '';
            $page = max(1, intval($_GET['page'] ?? 1));

            $result = $fontManager->getFonts($category, $search, $page);
            successResponse($result);
            break;

        case 'detail':
            $id = intval($_GET['id'] ?? 0);
            if (!$id) {
                errorResponse('字体ID不能为空');
            }

            $font = $fontManager->getFontById($id);
            if (!$font) {
                errorResponse('字体不存在', 404);
            }

            // 获取相关字体
            $relatedFonts = $fontManager->getRelatedFonts($id);

            successResponse([
                'font' => $font,
                'related_fonts' => $relatedFonts
            ]);
            break;

        case 'categories':
            $stats = $fontManager->getCategoryStats();
            $categories = [];

            foreach (FONT_CATEGORIES as $key => $name) {
                $categories[] = [
                    'key' => $key,
                    'name' => $name,
                    'count' => $stats[$key] ?? 0
                ];
            }

            successResponse(['categories' => $categories]);
            break;

        case 'download':
            $id = intval($_GET['id'] ?? 0);
            if (!$id) {
                errorResponse('字体ID不能为空');
            }

            $font = $fontManager->getFontById($id);
            if (!$font || !file_exists($font['filepath'])) {
                errorResponse('字体文件不存在', 404);
            }

            // 增加下载计数
            $fontManager->incrementDownloadCount($id);

            // 设置下载头
            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename="' . $font['filename'] . '"');
            header('Content-Length: ' . filesize($font['filepath']));

            readfile($font['filepath']);
            exit;

        case 'get_preview_text':
            successResponse(['preview_text' => $_SESSION['preview_text']]);
            break;

        default:
            errorResponse('未知操作');
    }
}

function handlePostRequest($action) {
    global $fontManager;

    switch ($action) {
        case 'search':
            $input = json_decode(file_get_contents('php://input'), true);
            $query = $input['query'] ?? '';
            $page = max(1, intval($input['page'] ?? 1));

            if (empty($query)) {
                errorResponse('搜索关键词不能为空');
            }

            $result = $fontManager->searchFonts($query, $page);
            successResponse($result);
            break;

        case 'set_preview_text':
            $input = json_decode(file_get_contents('php://input'), true);
            $text = $input['text'] ?? '';

            if (strlen($text) > 200) {
                errorResponse('预览文字不能超过200个字符');
            }

            $_SESSION['preview_text'] = $text;
            successResponse(['preview_text' => $text], '预览文字已更新');
            break;

        default:
            errorResponse('未知操作');
    }
}

function handlePutRequest($action) {
    global $fontManager;

    switch ($action) {
        case 'update':
            $input = json_decode(file_get_contents('php://input'), true);
            $id = intval($input['id'] ?? 0);

            if (!$id) {
                errorResponse('字体ID不能为空');
            }

            $font = $fontManager->getFontById($id);
            if (!$font) {
                errorResponse('字体不存在', 404);
            }

            $updateData = [];

            if (isset($input['name']) && !empty($input['name'])) {
                $updateData['name'] = trim($input['name']);
            }

            if (isset($input['description'])) {
                $updateData['description'] = trim($input['description']);
            }

            if (isset($input['tags'])) {
                $updateData['tags'] = trim($input['tags']);
            }

            if (isset($input['category']) && array_key_exists($input['category'], FONT_CATEGORIES)) {
                $updateData['category'] = $input['category'];
            }

            if (empty($updateData)) {
                errorResponse('没有要更新的数据');
            }

            $result = $fontManager->updateFont($id, $updateData);
            if ($result) {
                successResponse([], '字体信息已更新');
            } else {
                errorResponse('更新失败', 500);
            }
            break;

        default:
            errorResponse('未知操作');
    }
}

function handleDeleteRequest($action) {
    global $fontManager;

    switch ($action) {
        case 'delete':
            $input = json_decode(file_get_contents('php://input'), true);
            $id = intval($input['id'] ?? 0);

            if (!$id) {
                errorResponse('字体ID不能为空');
            }

            $result = $fontManager->deleteFont($id);
            if ($result) {
                successResponse([], '字体已删除');
            } else {
                errorResponse('删除失败', 500);
            }
            break;

        default:
            errorResponse('未知操作');
    }
}
