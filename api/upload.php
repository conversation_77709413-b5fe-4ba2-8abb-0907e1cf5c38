<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/font-parser.php';

header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    errorResponse('只支持POST请求', 405);
}

if (!isset($_FILES['fonts']) || empty($_FILES['fonts']['name'][0])) {
    errorResponse('请选择要上传的字体文件');
}

$uploadedFonts = [];
$errors = [];

// 处理多文件上传
$fileCount = count($_FILES['fonts']['name']);

for ($i = 0; $i < $fileCount; $i++) {
    $fileName = $_FILES['fonts']['name'][$i];
    $fileTmpName = $_FILES['fonts']['tmp_name'][$i];
    $fileSize = $_FILES['fonts']['size'][$i];
    $fileError = $_FILES['fonts']['error'][$i];
    
    // 跳过空文件
    if ($fileError === UPLOAD_ERR_NO_FILE) {
        continue;
    }
    
    // 检查上传错误
    if ($fileError !== UPLOAD_ERR_OK) {
        $errors[] = "文件 {$fileName} 上传失败: " . getUploadErrorMessage($fileError);
        continue;
    }
    
    // 检查文件大小
    if ($fileSize > MAX_FILE_SIZE) {
        $errors[] = "文件 {$fileName} 太大，最大允许 " . formatFileSize(MAX_FILE_SIZE);
        continue;
    }
    
    // 检查文件扩展名
    $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
    if (!in_array($fileExtension, ALLOWED_EXTENSIONS)) {
        $errors[] = "文件 {$fileName} 格式不支持，支持的格式: " . implode(', ', ALLOWED_EXTENSIONS);
        continue;
    }
    
    try {
        // 生成唯一文件名
        $uniqueFileName = generateUniqueFilename($fileName, FONTS_DIR);
        $targetPath = FONTS_DIR . $uniqueFileName;
        
        // 移动文件到目标目录
        if (!move_uploaded_file($fileTmpName, $targetPath)) {
            $errors[] = "文件 {$fileName} 保存失败";
            continue;
        }
        
        // 解析字体信息
        $parser = new FontParser($targetPath);
        $fontInfo = $parser->parse();
        
        // 准备数据库数据
        $fontData = [
            'name' => $fontInfo['name'],
            'filename' => $uniqueFileName,
            'filepath' => $targetPath,
            'family' => $fontInfo['family'],
            'style' => $fontInfo['style'],
            'weight' => $fontInfo['weight'],
            'category' => $fontInfo['category'],
            'file_size' => $fontInfo['file_size']
        ];
        
        // 保存到数据库
        $fontId = $fontManager->addFont($fontData);
        
        if ($fontId) {
            $uploadedFonts[] = [
                'id' => $fontId,
                'name' => $fontInfo['name'],
                'filename' => $uniqueFileName,
                'category' => $fontInfo['category'],
                'size' => formatFileSize($fontInfo['file_size'])
            ];
        } else {
            // 如果数据库保存失败，删除已上传的文件
            unlink($targetPath);
            $errors[] = "文件 {$fileName} 数据库保存失败";
        }
        
    } catch (Exception $e) {
        // 如果解析失败，删除已上传的文件
        if (file_exists($targetPath)) {
            unlink($targetPath);
        }
        $errors[] = "文件 {$fileName} 处理失败: " . $e->getMessage();
    }
}

// 返回结果
$response = [
    'success' => !empty($uploadedFonts),
    'uploaded' => $uploadedFonts,
    'errors' => $errors,
    'total_uploaded' => count($uploadedFonts),
    'total_errors' => count($errors)
];

if (!empty($uploadedFonts)) {
    $response['message'] = "成功上传 " . count($uploadedFonts) . " 个字体文件";
    if (!empty($errors)) {
        $response['message'] .= "，" . count($errors) . " 个文件上传失败";
    }
} else {
    $response['message'] = "所有文件上传失败";
}

echo json_encode($response, JSON_UNESCAPED_UNICODE);

function getUploadErrorMessage($errorCode) {
    switch ($errorCode) {
        case UPLOAD_ERR_INI_SIZE:
        case UPLOAD_ERR_FORM_SIZE:
            return '文件太大';
        case UPLOAD_ERR_PARTIAL:
            return '文件上传不完整';
        case UPLOAD_ERR_NO_TMP_DIR:
            return '临时目录不存在';
        case UPLOAD_ERR_CANT_WRITE:
            return '文件写入失败';
        case UPLOAD_ERR_EXTENSION:
            return '文件上传被扩展阻止';
        default:
            return '未知错误';
    }
}
