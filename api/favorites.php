<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/functions.php';

header('Content-Type: application/json; charset=utf-8');

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

switch ($method) {
    case 'GET':
        handleGetRequest($action);
        break;
    case 'POST':
        handlePostRequest($action);
        break;
    case 'DELETE':
        handleDeleteRequest($action);
        break;
    default:
        errorResponse('不支持的请求方法', 405);
}

function handleGetRequest($action) {
    global $fontManager;
    
    switch ($action) {
        case 'list':
            $favorites = $fontManager->getFavorites();
            
            // 添加收藏状态
            foreach ($favorites as &$font) {
                $font['is_favorite'] = true;
                $font['is_in_comparison'] = $fontManager->isInComparison($font['id']);
            }
            
            successResponse(['favorites' => $favorites]);
            break;
            
        default:
            errorResponse('未知操作');
    }
}

function handlePostRequest($action) {
    global $fontManager;
    
    switch ($action) {
        case 'add':
            $input = json_decode(file_get_contents('php://input'), true);
            $fontId = intval($input['font_id'] ?? 0);
            
            if (!$fontId) {
                errorResponse('字体ID不能为空');
            }
            
            // 检查字体是否存在
            $font = $fontManager->getFontById($fontId);
            if (!$font) {
                errorResponse('字体不存在', 404);
            }
            
            // 检查是否已经收藏
            if ($fontManager->isFavorite($fontId)) {
                errorResponse('字体已在收藏列表中');
            }
            
            $result = $fontManager->addToFavorites($fontId);
            if ($result) {
                successResponse([], '已添加到收藏');
            } else {
                errorResponse('添加收藏失败', 500);
            }
            break;
            
        default:
            errorResponse('未知操作');
    }
}

function handleDeleteRequest($action) {
    global $fontManager;
    
    switch ($action) {
        case 'remove':
            $input = json_decode(file_get_contents('php://input'), true);
            $fontId = intval($input['font_id'] ?? 0);
            
            if (!$fontId) {
                errorResponse('字体ID不能为空');
            }
            
            $result = $fontManager->removeFromFavorites($fontId);
            if ($result) {
                successResponse([], '已从收藏中移除');
            } else {
                errorResponse('移除收藏失败', 500);
            }
            break;
            
        default:
            errorResponse('未知操作');
    }
}
