<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/functions.php';

header('Content-Type: application/json; charset=utf-8');

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

switch ($method) {
    case 'GET':
        handleGetRequest($action);
        break;
    case 'POST':
        handlePostRequest($action);
        break;
    case 'DELETE':
        handleDeleteRequest($action);
        break;
    default:
        errorResponse('不支持的请求方法', 405);
}

function handleGetRequest($action) {
    global $fontManager;
    
    switch ($action) {
        case 'list':
            $comparisons = $fontManager->getComparisons();
            
            // 添加对比状态
            foreach ($comparisons as &$font) {
                $font['is_favorite'] = $fontManager->isFavorite($font['id']);
                $font['is_in_comparison'] = true;
            }
            
            successResponse(['comparisons' => $comparisons]);
            break;
            
        default:
            errorResponse('未知操作');
    }
}

function handlePostRequest($action) {
    global $fontManager;
    
    switch ($action) {
        case 'add':
            $input = json_decode(file_get_contents('php://input'), true);
            $fontId = intval($input['font_id'] ?? 0);
            
            if (!$fontId) {
                errorResponse('字体ID不能为空');
            }
            
            // 检查字体是否存在
            $font = $fontManager->getFontById($fontId);
            if (!$font) {
                errorResponse('字体不存在', 404);
            }
            
            // 检查是否已经在对比列表中
            if ($fontManager->isInComparison($fontId)) {
                errorResponse('字体已在对比列表中');
            }
            
            // 检查对比列表数量限制（最多10个）
            $currentComparisons = $fontManager->getComparisons();
            if (count($currentComparisons) >= 10) {
                errorResponse('对比列表最多只能添加10个字体');
            }
            
            $result = $fontManager->addToComparison($fontId);
            if ($result) {
                successResponse([], '已添加到对比列表');
            } else {
                errorResponse('添加到对比列表失败', 500);
            }
            break;
            
        case 'clear':
            $result = $fontManager->clearComparisons();
            if ($result) {
                successResponse([], '对比列表已清空');
            } else {
                errorResponse('清空对比列表失败', 500);
            }
            break;
            
        default:
            errorResponse('未知操作');
    }
}

function handleDeleteRequest($action) {
    global $fontManager;
    
    switch ($action) {
        case 'remove':
            $input = json_decode(file_get_contents('php://input'), true);
            $fontId = intval($input['font_id'] ?? 0);
            
            if (!$fontId) {
                errorResponse('字体ID不能为空');
            }
            
            $result = $fontManager->removeFromComparison($fontId);
            if ($result) {
                successResponse([], '已从对比列表中移除');
            } else {
                errorResponse('从对比列表移除失败', 500);
            }
            break;
            
        default:
            errorResponse('未知操作');
    }
}
