RewriteEngine On

# 处理字体文件的MIME类型
AddType font/ttf .ttf
AddType font/otf .otf
AddType font/woff .woff
AddType font/woff2 .woff2

# 启用字体文件的跨域访问
<FilesMatch "\.(ttf|otf|woff|woff2)$">
    Header set Access-Control-Allow-Origin "*"
</FilesMatch>

# 缓存字体文件
<FilesMatch "\.(ttf|otf|woff|woff2)$">
    ExpiresActive On
    ExpiresDefault "access plus 1 month"
</FilesMatch>

# 重写规则
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# API路由
RewriteRule ^api/(.*)$ api/$1 [L]

# 静态资源
RewriteRule ^assets/(.*)$ assets/$1 [L]

# 字体详情页
RewriteRule ^font/([0-9]+)/?$ font-detail.php?id=$1 [L]

# 收藏页面
RewriteRule ^favorites/?$ favorites.php [L]

# 对比页面
RewriteRule ^compare/?$ compare.php [L]

# 默认首页
RewriteRule ^$ index.php [L]
